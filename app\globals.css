@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    /* Enhanced color palette */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

    /* Glass morphism */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Enhanced shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.5);

    /* Enhanced spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    /* Dark mode glass morphism */
    --glass-bg: rgba(0, 0, 0, 0.2);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);

    /* Dark mode enhanced shadows */
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Improve mobile scrolling */
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent horizontal overflow on mobile */
  html,
  body {
    overflow-x: hidden;
    max-width: 100vw;
  }
}

/* Custom scrollbar for dark mode */
.dark ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.dark ::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.dark ::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Custom scrollbar for light mode */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Responsive table improvements */
@media (max-width: 640px) {
  .table-container {
    margin: 0 -1rem;
    width: calc(100% + 2rem);
  }

  .table-inner {
    padding: 0 1rem;
  }

  .recharts-wrapper .recharts-cartesian-grid-horizontal line,
  .recharts-wrapper .recharts-cartesian-grid-vertical line {
    stroke: rgba(0, 0, 0, 0.1);
    stroke-width: 0.5;
  }

  .recharts-wrapper .recharts-cartesian-axis-tick-value {
    font-size: 10px;
  }

  .recharts-wrapper .recharts-legend-item-text {
    font-size: 10px;
  }

  .recharts-tooltip-wrapper .recharts-default-tooltip {
    padding: 6px !important;
  }

  .recharts-tooltip-wrapper .recharts-default-tooltip .recharts-tooltip-label {
    font-size: 10px !important;
    margin-bottom: 2px !important;
  }

  .recharts-tooltip-wrapper .recharts-default-tooltip .recharts-tooltip-item {
    font-size: 10px !important;
    padding: 1px 0 !important;
  }

  /* Improve touch targets */
  button,
  a {
    min-height: 40px;
  }

  /* Adjust card padding */
  .card-header {
    padding: 12px !important;
  }

  .card-content {
    padding: 12px !important;
  }

  /* Adjust typography */
  h1,
  h2,
  h3 {
    line-height: 1.3;
  }

  /* Improve table readability */
  table {
    font-size: 12px;
  }

  /* Ensure form elements are easily tappable */
  input,
  select,
  textarea {
    min-height: 40px;
    font-size: 16px; /* Prevents iOS zoom on focus */
  }
}

/* Add extra small screen breakpoint */
@media (min-width: 400px) {
  .xs\:w-auto {
    width: auto;
  }

  .xs\:p-3 {
    padding: 0.75rem;
  }

  .xs\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .xs\:h-200px {
    height: 200px;
  }

  .xs\:max-w-\[180px\] {
    max-width: 180px;
  }

  .xs\:gap-3 {
    gap: 0.75rem;
  }
}

/* Improve touch targets for mobile */
@media (max-width: 640px) {
  button,
  a,
  [role="button"],
  input[type="checkbox"],
  input[type="radio"] {
    min-height: 36px;
    min-width: 36px;
  }

  .touch-target-fix {
    position: relative;
  }

  .touch-target-fix::after {
    content: "";
    position: absolute;
    top: -8px;
    right: -8px;
    bottom: -8px;
    left: -8px;
    z-index: -1;
  }
}

/* Improve mobile card styles */
@media (max-width: 640px) {
  .mobile-card {
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    padding: 0.75rem;
    margin-bottom: 0.5rem;
  }
}

/* Prevent horizontal overflow */
.overflow-guard {
  max-width: 100%;
  overflow-x: hidden;
}

/* Responsive spacing utility classes */
.p-responsive {
  padding: 0.75rem;
}

@media (min-width: 640px) {
  .p-responsive {
    padding: 1rem;
  }
}

@media (min-width: 768px) {
  .p-responsive {
    padding: 1.5rem;
  }
}

/* Improve chart responsiveness */
.chart-container {
  width: 100% !important;
  height: auto !important;
  min-height: 200px;
}

/* Improve mobile navigation */
.mobile-nav-item {
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.mobile-nav-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}

@layer components {
  /* Glass morphism utilities */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  /* Enhanced card styles */
  .card-enhanced {
    @apply bg-card/50 backdrop-blur-sm border border-border/50 shadow-lg hover:shadow-xl transition-all duration-300;
  }

  /* Gradient text utilities */
  .gradient-text-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-text-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced button styles */
  .btn-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    @apply text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105;
  }

  .btn-gradient-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    @apply text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105;
  }

  /* Floating animation */
  .float {
    animation: float 6s ease-in-out infinite;
  }

  /* Pulse glow animation */
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  /* Shimmer effect */
  .shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  /* Enhanced hover effects */
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-glow;
  }
}

@layer utilities {
  /* Custom animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes pulse-glow {
    0% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
    100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.4); }
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Animation utilities */
  .animate-slide-in-up {
    animation: slideInUp 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
  }

  .animate-fade-in-scale {
    animation: fadeInScale 0.6s ease-out;
  }

  /* Stagger animation delays */
  .animate-delay-100 { animation-delay: 100ms; }
  .animate-delay-200 { animation-delay: 200ms; }
  .animate-delay-300 { animation-delay: 300ms; }
  .animate-delay-400 { animation-delay: 400ms; }
  .animate-delay-500 { animation-delay: 500ms; }
}
