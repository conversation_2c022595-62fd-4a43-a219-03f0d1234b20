@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Improve mobile scrolling */
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent horizontal overflow on mobile */
  html,
  body {
    overflow-x: hidden;
    max-width: 100vw;
  }
}

/* Custom scrollbar for dark mode */
.dark ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.dark ::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.dark ::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Custom scrollbar for light mode */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Responsive table improvements */
@media (max-width: 640px) {
  .table-container {
    margin: 0 -1rem;
    width: calc(100% + 2rem);
  }

  .table-inner {
    padding: 0 1rem;
  }

  .recharts-wrapper .recharts-cartesian-grid-horizontal line,
  .recharts-wrapper .recharts-cartesian-grid-vertical line {
    stroke: rgba(0, 0, 0, 0.1);
    stroke-width: 0.5;
  }

  .recharts-wrapper .recharts-cartesian-axis-tick-value {
    font-size: 10px;
  }

  .recharts-wrapper .recharts-legend-item-text {
    font-size: 10px;
  }

  .recharts-tooltip-wrapper .recharts-default-tooltip {
    padding: 6px !important;
  }

  .recharts-tooltip-wrapper .recharts-default-tooltip .recharts-tooltip-label {
    font-size: 10px !important;
    margin-bottom: 2px !important;
  }

  .recharts-tooltip-wrapper .recharts-default-tooltip .recharts-tooltip-item {
    font-size: 10px !important;
    padding: 1px 0 !important;
  }

  /* Improve touch targets */
  button,
  a {
    min-height: 40px;
  }

  /* Adjust card padding */
  .card-header {
    padding: 12px !important;
  }

  .card-content {
    padding: 12px !important;
  }

  /* Adjust typography */
  h1,
  h2,
  h3 {
    line-height: 1.3;
  }

  /* Improve table readability */
  table {
    font-size: 12px;
  }

  /* Ensure form elements are easily tappable */
  input,
  select,
  textarea {
    min-height: 40px;
    font-size: 16px; /* Prevents iOS zoom on focus */
  }
}

/* Add extra small screen breakpoint */
@media (min-width: 400px) {
  .xs\:w-auto {
    width: auto;
  }

  .xs\:p-3 {
    padding: 0.75rem;
  }

  .xs\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .xs\:h-200px {
    height: 200px;
  }

  .xs\:max-w-\[180px\] {
    max-width: 180px;
  }

  .xs\:gap-3 {
    gap: 0.75rem;
  }
}

/* Improve touch targets for mobile */
@media (max-width: 640px) {
  button,
  a,
  [role="button"],
  input[type="checkbox"],
  input[type="radio"] {
    min-height: 36px;
    min-width: 36px;
  }

  .touch-target-fix {
    position: relative;
  }

  .touch-target-fix::after {
    content: "";
    position: absolute;
    top: -8px;
    right: -8px;
    bottom: -8px;
    left: -8px;
    z-index: -1;
  }
}

/* Improve mobile card styles */
@media (max-width: 640px) {
  .mobile-card {
    border-radius: var(--radius);
    border: 1px solid hsl(var(--border));
    padding: 0.75rem;
    margin-bottom: 0.5rem;
  }
}

/* Prevent horizontal overflow */
.overflow-guard {
  max-width: 100%;
  overflow-x: hidden;
}

/* Responsive spacing utility classes */
.p-responsive {
  padding: 0.75rem;
}

@media (min-width: 640px) {
  .p-responsive {
    padding: 1rem;
  }
}

@media (min-width: 768px) {
  .p-responsive {
    padding: 1.5rem;
  }
}

/* Improve chart responsiveness */
.chart-container {
  width: 100% !important;
  height: auto !important;
  min-height: 200px;
}

/* Improve mobile navigation */
.mobile-nav-item {
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.mobile-nav-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}
