import type React from "react"
import { SidebarProvider } from "@/components/ui/sidebar"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"
import { Toaster } from "@/components/ui/toaster"
import { ThemeToggle } from "@/components/theme-toggle"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex min-h-screen flex-col md:flex-row bg-gradient-to-br from-slate-50/50 via-blue-50/20 to-indigo-50/30 dark:from-slate-950/50 dark:via-slate-900/50 dark:to-slate-950/50">
        <DashboardSidebar />
        <div className="flex-1 flex flex-col min-h-screen max-w-full">
          <DashboardHeader />
          <main className="flex-1 p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8 overflow-auto relative">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(59,130,246,0.05)_1px,transparent_1px)] bg-[length:50px_50px] pointer-events-none"></div>
            <div className="relative z-10">{children}</div>
          </main>
        </div>
      </div>
      <div className="fixed bottom-6 right-6 z-50">
        <ThemeToggle />
      </div>
      <Toaster />
    </SidebarProvider>
  )
}
