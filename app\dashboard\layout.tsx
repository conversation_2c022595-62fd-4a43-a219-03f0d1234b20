import type React from "react"
import { SidebarProvider } from "@/components/ui/sidebar"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"
import { Toaster } from "@/components/ui/toaster"
import { ThemeToggle } from "@/components/theme-toggle"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex min-h-screen flex-col md:flex-row bg-background">
        <DashboardSidebar />
        <div className="flex-1 flex flex-col min-h-screen max-w-full">
          <DashboardHeader />
          <main className="flex-1 p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8 overflow-auto">{children}</main>
        </div>
      </div>
      <div className="fixed bottom-4 right-4 z-50">
        <ThemeToggle />
      </div>
      <Toaster />
    </SidebarProvider>
  )
}
