"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Send, ShoppingCart, CreditCard, Check } from "lucide-react"

type MessageType = {
  role: "user" | "assistant" | "system"
  content: string
  timestamp: Date
}

type OrderStep = "inquiry" | "product" | "checkout" | "confirmation" | null

export default function ChatDemo() {
  const [messages, setMessages] = useState<MessageType[]>([
    {
      role: "system",
      content:
        "Welcome to Syda AI Demo! This simulates how our AI assistant would interact with your customers on WhatsApp or Instagram. Try asking about products, placing an order, or asking for support.",
      timestamp: new Date(),
    },
  ])
  const [input, setInput] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const [orderStep, setOrderStep] = useState<OrderStep>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  const simulateTyping = (message: string, delay = 1500) => {
    setIsTyping(true)
    setTimeout(() => {
      setMessages((prev) => [...prev, { role: "assistant", content: message, timestamp: new Date() }])
      setIsTyping(false)
    }, delay)
  }

  const handleSend = () => {
    if (!input.trim()) return

    // Add user message
    const userMessage = {
      role: "user" as const,
      content: input,
      timestamp: new Date(),
    }
    setMessages((prev) => [...prev, userMessage])
    setInput("")

    // Process based on current order step or message content
    const lowerInput = input.toLowerCase()

    if (
      orderStep === "inquiry" ||
      lowerInput.includes("product") ||
      lowerInput.includes("buy") ||
      lowerInput.includes("purchase")
    ) {
      setOrderStep("product")
      simulateTyping(
        "We have several products available. Our bestseller is the Premium T-shirt for ₦5,000. Would you like to order this item? You can also ask for other products we have in stock.",
        1000,
      )
    } else if (
      orderStep === "product" ||
      lowerInput.includes("yes") ||
      lowerInput.includes("order") ||
      lowerInput.includes("buy")
    ) {
      setOrderStep("checkout")
      simulateTyping(
        "Great choice! How many Premium T-shirts would you like to order? We have sizes S, M, L, and XL available.",
        1000,
      )

      // After a delay, proceed to payment
      setTimeout(() => {
        simulateTyping(
          "I've added 1 Premium T-shirt (Size M) to your order. The total is ₦5,000. Would you like to proceed to payment?",
          1000,
        )
      }, 3000)
    } else if (orderStep === "checkout" || lowerInput.includes("pay") || lowerInput.includes("payment")) {
      setOrderStep("confirmation")
      simulateTyping(
        "Perfect! Here's your payment link: https://payment.sydaai.com/t5k7j9\n\nOnce you complete the payment, I'll confirm your order immediately.",
        1000,
      )

      // Simulate payment confirmation after a delay
      setTimeout(() => {
        simulateTyping(
          "Payment received! Your order #SMT12345 has been confirmed. Your Premium T-shirt will be shipped within 24 hours. You'll receive tracking information once it's dispatched. Thank you for shopping with us!",
          1500,
        )

        // Reset order flow
        setTimeout(() => {
          setOrderStep(null)
        }, 2000)
      }, 5000)
    } else if (lowerInput.includes("hello") || lowerInput.includes("hi") || lowerInput.includes("hey")) {
      simulateTyping(
        "Hello! Welcome to Usman's Store. How can I assist you today? You can ask about our products, place an order, or get support with an existing order.",
        800,
      )
    } else if (lowerInput.includes("help") || lowerInput.includes("support")) {
      simulateTyping(
        "I'm here to help! You can ask me about:\n\n• Our products and pricing\n• Placing an order\n• Checking order status\n• Return policy\n• Store hours and location\n\nWhat would you like to know?",
        1000,
      )
    } else if (lowerInput.includes("hours") || lowerInput.includes("location") || lowerInput.includes("address")) {
      simulateTyping(
        "Our physical store is open Monday to Saturday, 9 AM to 6 PM. We're located at 123 Main Street, Lagos. But you can shop online 24/7 and I'm always here to assist you!",
        1000,
      )
    } else if (lowerInput.includes("return") || lowerInput.includes("refund")) {
      simulateTyping(
        "We offer a 14-day return policy for all unused items in original packaging. To initiate a return, please provide your order number and I'll guide you through the process.",
        1000,
      )
    } else {
      setOrderStep("inquiry")
      simulateTyping(
        "Thanks for your message! I'd be happy to help you with that. Are you interested in browsing our products or do you have a specific item in mind?",
        1000,
      )
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950">
      <header className="glass border-b border-border/50 backdrop-blur-xl">
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3 animate-fade-in-scale">
            <Avatar className="h-12 w-12 ring-2 ring-blue-500/20 shadow-lg">
              <AvatarImage src="/placeholder.svg?height=48&width=48" alt="Store Logo" />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold">US</AvatarFallback>
            </Avatar>
            <div>
              <h1 className="font-bold text-lg gradient-text-primary">Usman's Store</h1>
              <p className="text-sm text-muted-foreground flex items-center gap-1">
                <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                WhatsApp Business
              </p>
            </div>
          </div>
          <Button variant="outline" size="sm" onClick={() => window.history.back()} className="hover-lift">
            Back to Demo
          </Button>
        </div>
      </header>

      <main className="flex-1 container mx-auto px-6 py-8 flex flex-col md:flex-row gap-8">
        <div className="w-full md:w-2/3 flex flex-col animate-slide-in-up">
          <Card className="flex-1 card-enhanced shadow-2xl">
            <CardHeader className="border-b border-border/50 pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl gradient-text-primary">Chat with AI Assistant</CardTitle>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                  Online
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="h-[600px] overflow-y-auto p-6 flex flex-col gap-4 bg-gradient-to-b from-background/50 to-background/80">
                {messages.map((message, index) => (
                  <div key={index} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"} animate-slide-in-up`} style={{animationDelay: `${index * 100}ms`}}>
                    {message.role === "assistant" && (
                      <Avatar className="h-10 w-10 mr-3 mt-1 ring-2 ring-blue-500/20 shadow-lg">
                        <AvatarImage src="/placeholder.svg?height=40&width=40" alt="AI" />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-sm">AI</AvatarFallback>
                      </Avatar>
                    )}
                    <div
                      className={`max-w-[80%] p-4 rounded-2xl shadow-lg hover-lift ${
                        message.role === "user"
                          ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white"
                          : message.role === "system"
                            ? "glass border border-border/50 text-foreground max-w-full"
                            : "bg-card border border-border/50 text-foreground"
                      }`}
                    >
                      <div className="whitespace-pre-wrap leading-relaxed">{message.content}</div>
                      <div className={`text-xs mt-2 ${message.role === "user" ? "text-blue-100" : "text-muted-foreground"}`}>
                        {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                      </div>
                    </div>
                  </div>
                ))}
                {isTyping && (
                  <div className="flex justify-start animate-fade-in-scale">
                    <Avatar className="h-10 w-10 mr-3 mt-1 ring-2 ring-blue-500/20 shadow-lg">
                      <AvatarImage src="/placeholder.svg?height=40&width=40" alt="AI" />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-sm">AI</AvatarFallback>
                    </Avatar>
                    <div className="bg-card border border-border/50 p-4 rounded-2xl shadow-lg">
                      <div className="flex gap-1">
                        <div
                          className="w-3 h-3 rounded-full bg-blue-500 animate-bounce"
                          style={{ animationDelay: "0ms" }}
                        ></div>
                        <div
                          className="w-3 h-3 rounded-full bg-blue-500 animate-bounce"
                          style={{ animationDelay: "150ms" }}
                        ></div>
                        <div
                          className="w-3 h-3 rounded-full bg-blue-500 animate-bounce"
                          style={{ animationDelay: "300ms" }}
                        ></div>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
              <div className="p-6 border-t border-border/50 glass">
                <div className="flex gap-3">
                  <Input
                    placeholder="Type your message..."
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="flex-1 rounded-xl border-border/50 bg-background/50 backdrop-blur-sm focus:ring-2 focus:ring-blue-500/20"
                  />
                  <Button
                    onClick={handleSend}
                    disabled={isTyping}
                    className="btn-gradient-primary rounded-xl px-6 shadow-lg hover:shadow-xl"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="w-full md:w-1/3 animate-slide-in-right animate-delay-200">
          <Card className="card-enhanced shadow-2xl">
            <CardHeader className="border-b border-border/50 pb-4">
              <CardTitle className="text-xl gradient-text-primary">Order Process Demo</CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="flex flex-col gap-8">
                <div className="flex items-start gap-4 group hover-lift">
                  <div
                    className={`rounded-xl p-3 transition-all duration-300 ${orderStep ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg" : "bg-muted text-muted-foreground"}`}
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-foreground group-hover:text-blue-600 transition-colors">Product Inquiry</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">AI answers product questions and makes recommendations</p>
                  </div>
                </div>

                <div className="flex items-start gap-4 group hover-lift">
                  <div
                    className={`rounded-xl p-3 transition-all duration-300 ${orderStep === "product" || orderStep === "checkout" || orderStep === "confirmation" ? "bg-gradient-to-br from-purple-500 to-purple-600 text-white shadow-lg" : "bg-muted text-muted-foreground"}`}
                  >
                    <ShoppingCart className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-foreground group-hover:text-purple-600 transition-colors">Order Creation</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">Customer selects products and quantities</p>
                  </div>
                </div>

                <div className="flex items-start gap-4 group hover-lift">
                  <div
                    className={`rounded-xl p-3 transition-all duration-300 ${orderStep === "checkout" || orderStep === "confirmation" ? "bg-gradient-to-br from-teal-500 to-teal-600 text-white shadow-lg" : "bg-muted text-muted-foreground"}`}
                  >
                    <CreditCard className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-foreground group-hover:text-teal-600 transition-colors">Payment Processing</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">AI generates payment link and processes transaction</p>
                  </div>
                </div>

                <div className="flex items-start gap-4 group hover-lift">
                  <div
                    className={`rounded-xl p-3 transition-all duration-300 ${orderStep === "confirmation" ? "bg-gradient-to-br from-green-500 to-green-600 text-white shadow-lg pulse-glow" : "bg-muted text-muted-foreground"}`}
                  >
                    <Check className="h-5 w-5" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-foreground group-hover:text-green-600 transition-colors">Order Confirmation</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">Order is confirmed and business is notified</p>
                  </div>
                </div>
              </div>

              <div className="mt-8 p-6 glass rounded-2xl border border-border/50">
                <h3 className="font-semibold mb-4 gradient-text-primary">Try these prompts:</h3>
                <ul className="space-y-3">
                  <li
                    className="cursor-pointer p-3 rounded-xl bg-background/50 hover:bg-blue-50 dark:hover:bg-blue-950/30 border border-border/30 hover:border-blue-500/50 transition-all duration-300 hover-lift"
                    onClick={() => {
                      setInput("Hello, I'm interested in your products")
                      handleSend()
                    }}
                  >
                    <span className="text-sm font-medium">"Hello, I'm interested in your products"</span>
                  </li>
                  <li
                    className="cursor-pointer p-3 rounded-xl bg-background/50 hover:bg-purple-50 dark:hover:bg-purple-950/30 border border-border/30 hover:border-purple-500/50 transition-all duration-300 hover-lift"
                    onClick={() => {
                      setInput("I want to buy a t-shirt")
                      handleSend()
                    }}
                  >
                    <span className="text-sm font-medium">"I want to buy a t-shirt"</span>
                  </li>
                  <li
                    className="cursor-pointer p-3 rounded-xl bg-background/50 hover:bg-teal-50 dark:hover:bg-teal-950/30 border border-border/30 hover:border-teal-500/50 transition-all duration-300 hover-lift"
                    onClick={() => {
                      setInput("What's your return policy?")
                      handleSend()
                    }}
                  >
                    <span className="text-sm font-medium">"What's your return policy?"</span>
                  </li>
                  <li
                    className="cursor-pointer p-3 rounded-xl bg-background/50 hover:bg-green-50 dark:hover:bg-green-950/30 border border-border/30 hover:border-green-500/50 transition-all duration-300 hover-lift"
                    onClick={() => {
                      setInput("What are your store hours?")
                      handleSend()
                    }}
                  >
                    <span className="text-sm font-medium">"What are your store hours?"</span>
                  </li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
