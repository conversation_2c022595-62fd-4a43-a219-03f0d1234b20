import type { Metadata } from "next"
import { SalesOverview } from "@/components/sales-overview"
import { CustomerInsights } from "@/components/customer-insights"
import { RecentOrders } from "@/components/recent-orders"
import { InventoryStatus } from "@/components/inventory-status"
import { ResponsiveDashboardLayout } from "@/components/responsive-dashboard-layout"

export const metadata: Metadata = {
  title: "Dashboard",
  description: "Example dashboard app built using the components.",
}

export default function DashboardPage() {
  return (
    <div className="flex flex-col gap-6 p-4 md:p-8 pt-6 bg-gradient-to-br from-slate-50/50 via-blue-50/30 to-indigo-50/50 dark:from-slate-950/50 dark:via-slate-900/50 dark:to-slate-950/50 min-h-screen">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <div className="card-enhanced rounded-2xl p-6 group hover-lift animate-fade-in-scale">
          <div className="flex flex-row items-center justify-between space-y-0 pb-3">
            <h3 className="tracking-tight text-sm font-medium text-muted-foreground">Total Revenue</h3>
            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
              <span className="text-white text-xs font-bold">₦</span>
            </div>
          </div>
          <div className="flex items-center">
            <div className="text-3xl font-bold gradient-text-primary">₦45,231.89</div>
            <div className="ml-3 text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">+20.1%</div>
          </div>
        </div>
        <div className="card-enhanced rounded-2xl p-6 group hover-lift animate-fade-in-scale animate-delay-100">
          <div className="flex flex-row items-center justify-between space-y-0 pb-3">
            <h3 className="tracking-tight text-sm font-medium text-muted-foreground">Subscriptions</h3>
            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
              <span className="text-white text-xs font-bold">👥</span>
            </div>
          </div>
          <div className="flex items-center">
            <div className="text-3xl font-bold gradient-text-primary">+2,350</div>
            <div className="ml-3 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium">+180.1%</div>
          </div>
        </div>
        <div className="card-enhanced rounded-2xl p-6 group hover-lift animate-fade-in-scale animate-delay-200">
          <div className="flex flex-row items-center justify-between space-y-0 pb-3">
            <h3 className="tracking-tight text-sm font-medium text-muted-foreground">Sales</h3>
            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
              <span className="text-white text-xs font-bold">📊</span>
            </div>
          </div>
          <div className="flex items-center">
            <div className="text-3xl font-bold gradient-text-primary">+12,234</div>
            <div className="ml-3 text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full font-medium">+19%</div>
          </div>
        </div>
        <div className="card-enhanced rounded-2xl p-6 group hover-lift animate-fade-in-scale animate-delay-300">
          <div className="flex flex-row items-center justify-between space-y-0 pb-3">
            <h3 className="tracking-tight text-sm font-medium text-muted-foreground">Active Now</h3>
            <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center pulse-glow">
              <span className="text-white text-xs font-bold">🔥</span>
            </div>
          </div>
          <div className="flex items-center">
            <div className="text-3xl font-bold gradient-text-primary">+573</div>
            <div className="ml-3 text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded-full font-medium">+201 since last hour</div>
          </div>
        </div>
      </div>
      <ResponsiveDashboardLayout className="md:grid-cols-2 lg:grid-cols-7 gap-6">
        <SalesOverview className="lg:col-span-4 animate-slide-in-up animate-delay-400" />
        <CustomerInsights className="lg:col-span-3 animate-slide-in-up animate-delay-500" />
      </ResponsiveDashboardLayout>
      <ResponsiveDashboardLayout className="md:grid-cols-2 lg:grid-cols-7 gap-6">
        <RecentOrders className="lg:col-span-4 animate-slide-in-up animate-delay-600" />
        <InventoryStatus className="lg:col-span-3 animate-slide-in-up animate-delay-700" />
      </ResponsiveDashboardLayout>
    </div>
  )
}
